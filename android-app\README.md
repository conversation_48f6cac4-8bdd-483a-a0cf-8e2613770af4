# Flori Construction Android App

This directory contains the native Android app for Flori Construction Ltd admin panel.

## Project Structure

```
android-app/
├── app/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/flori/construction/admin/
│   │   │   │   ├── MainActivity.java
│   │   │   │   ├── WebViewActivity.java
│   │   │   │   ├── NotificationService.java
│   │   │   │   ├── SyncService.java
│   │   │   │   └── utils/
│   │   │   ├── res/
│   │   │   │   ├── layout/
│   │   │   │   ├── values/
│   │   │   │   ├── drawable/
│   │   │   │   └── mipmap/
│   │   │   └── AndroidManifest.xml
│   │   └── androidTest/
│   ├── build.gradle
│   └── proguard-rules.pro
├── gradle/
├── build.gradle
├── settings.gradle
└── gradle.properties
```

## Features

### Core Features
- ✅ WebView wrapper for existing PWA
- ✅ Native Android 7.0+ compatibility
- ✅ Offline data synchronization
- ✅ Push notifications
- ✅ File upload with native picker
- ✅ Background sync
- ✅ Network status monitoring

### Security Features
- ✅ SSL certificate pinning
- ✅ Token-based authentication
- ✅ Secure storage for credentials
- ✅ App-level security checks

### Performance Features
- ✅ Optimized WebView settings
- ✅ Caching strategies
- ✅ Background processing
- ✅ Memory management

## Development Requirements

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24+ (Android 7.0+)
- Java 8 or Kotlin
- Gradle 7.0+

### Dependencies
- WebView support
- Firebase Cloud Messaging (FCM)
- OkHttp for networking
- Room database for offline storage
- WorkManager for background tasks

## Setup Instructions

1. **Create New Android Project**
   ```bash
   # Open Android Studio
   # Create new project with:
   # - Minimum SDK: API 24 (Android 7.0)
   # - Language: Java/Kotlin
   # - Template: Empty Activity
   ```

2. **Configure Gradle Dependencies**
   ```gradle
   implementation 'androidx.webkit:webkit:1.4.0'
   implementation 'com.google.firebase:firebase-messaging:23.0.0'
   implementation 'androidx.work:work-runtime:2.7.1'
   implementation 'androidx.room:room-runtime:2.4.0'
   implementation 'com.squareup.okhttp3:okhttp:4.9.3'
   ```

3. **Configure Permissions**
   ```xml
   <uses-permission android:name="android.permission.INTERNET" />
   <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
   <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
   <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
   <uses-permission android:name="android.permission.CAMERA" />
   <uses-permission android:name="android.permission.WAKE_LOCK" />
   ```

4. **Setup Firebase**
   - Add google-services.json to app/
   - Configure FCM for push notifications
   - Setup server key in PHP backend

## Implementation Plan

### Phase 1: Basic WebView App
1. Create MainActivity with WebView
2. Configure WebView settings for PWA
3. Handle navigation and back button
4. Add splash screen and loading states

### Phase 2: Native Features
1. Implement file upload with native picker
2. Add push notification handling
3. Create offline data storage
4. Implement background sync

### Phase 3: Security & Performance
1. Add SSL certificate pinning
2. Implement secure token storage
3. Optimize WebView performance
4. Add crash reporting

### Phase 4: Testing & Deployment
1. Unit and integration testing
2. Performance testing
3. Security testing
4. Play Store deployment

## Configuration

### WebView Settings
```java
webView.getSettings().setJavaScriptEnabled(true);
webView.getSettings().setDomStorageEnabled(true);
webView.getSettings().setDatabaseEnabled(true);
webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
webView.getSettings().setAppCacheEnabled(true);
```

### Push Notifications
- Server Key: Configure in Firebase Console
- Topic: 'flori-admin-updates'
- Payload: JSON with title, body, data

### Offline Storage
- SQLite database with Room
- Store projects, media, content
- Sync queue for pending operations

## Testing

### Device Testing
- Test on Android 7.0+ devices
- Various screen sizes and orientations
- Network connectivity scenarios
- Battery optimization settings

### Functional Testing
- Login/logout flows
- File upload functionality
- Push notification delivery
- Offline/online synchronization
- Background app behavior

## Deployment

### Build Configuration
```gradle
android {
    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 33
        versionCode 1
        versionName "1.0.0"
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

### Play Store Requirements
- App signing key
- Privacy policy
- App description and screenshots
- Content rating
- Target audience

## Maintenance

### Regular Updates
- Security patches
- Android version compatibility
- Feature enhancements
- Bug fixes

### Monitoring
- Crash reporting (Firebase Crashlytics)
- Performance monitoring
- User analytics
- Push notification metrics

## Support

For development support:
- Android Developer Documentation
- Firebase Documentation
- WebView Best Practices
- Material Design Guidelines

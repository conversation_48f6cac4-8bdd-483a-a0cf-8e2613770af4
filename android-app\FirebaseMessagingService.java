package com.flori.construction.admin.services;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import com.flori.construction.admin.MainActivity;
import com.flori.construction.admin.R;
import com.flori.construction.admin.workers.SyncWorker;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import java.util.Map;

/**
 * Firebase Cloud Messaging Service for Flori Construction Admin App
 * Handles push notifications and background sync triggers
 */
public class FirebaseMessagingService extends FirebaseMessagingService {
    
    private static final String TAG = "FCMService";
    private static final String CHANNEL_ID = "flori_admin_notifications";
    private static final String CHANNEL_NAME = "Flori Admin Notifications";
    private static final String CHANNEL_DESCRIPTION = "Notifications for Flori Construction Admin";
    
    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
    }
    
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Log.d(TAG, "From: " + remoteMessage.getFrom());
        
        // Check if message contains a data payload
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());
            handleDataMessage(remoteMessage.getData());
        }
        
        // Check if message contains a notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
            sendNotification(
                remoteMessage.getNotification().getTitle(),
                remoteMessage.getNotification().getBody(),
                remoteMessage.getData()
            );
        }
    }
    
    @Override
    public void onNewToken(String token) {
        Log.d(TAG, "Refreshed token: " + token);
        
        // Send token to server
        sendRegistrationToServer(token);
    }
    
    /**
     * Handle data messages for background processing
     */
    private void handleDataMessage(Map<String, String> data) {
        String type = data.get("type");
        
        if (type != null) {
            switch (type) {
                case "sync_request":
                    // Trigger background sync
                    triggerBackgroundSync();
                    break;
                    
                case "project_update":
                    // Handle project update
                    handleProjectUpdate(data);
                    break;
                    
                case "media_upload":
                    // Handle media upload notification
                    handleMediaUpload(data);
                    break;
                    
                case "system_notification":
                    // Handle system notifications
                    handleSystemNotification(data);
                    break;
                    
                default:
                    Log.w(TAG, "Unknown data message type: " + type);
            }
        }
    }
    
    /**
     * Trigger background sync using WorkManager
     */
    private void triggerBackgroundSync() {
        OneTimeWorkRequest syncWork = new OneTimeWorkRequest.Builder(SyncWorker.class)
            .addTag("fcm_sync")
            .build();
            
        WorkManager.getInstance(this).enqueue(syncWork);
    }
    
    /**
     * Handle project update notifications
     */
    private void handleProjectUpdate(Map<String, String> data) {
        String projectId = data.get("project_id");
        String projectTitle = data.get("project_title");
        String action = data.get("action");
        
        String title = "Project Update";
        String body = "Project '" + projectTitle + "' has been " + action;
        
        sendNotification(title, body, data);
    }
    
    /**
     * Handle media upload notifications
     */
    private void handleMediaUpload(Map<String, String> data) {
        String fileName = data.get("file_name");
        String status = data.get("status");
        
        String title = "Media Upload";
        String body = "File '" + fileName + "' " + status;
        
        sendNotification(title, body, data);
    }
    
    /**
     * Handle system notifications
     */
    private void handleSystemNotification(Map<String, String> data) {
        String title = data.get("title");
        String body = data.get("body");
        String priority = data.get("priority");
        
        int notificationPriority = NotificationCompat.PRIORITY_DEFAULT;
        if ("high".equals(priority)) {
            notificationPriority = NotificationCompat.PRIORITY_HIGH;
        } else if ("low".equals(priority)) {
            notificationPriority = NotificationCompat.PRIORITY_LOW;
        }
        
        sendNotification(title, body, data, notificationPriority);
    }
    
    /**
     * Create and show a simple notification containing the received FCM message
     */
    private void sendNotification(String title, String messageBody, Map<String, String> data) {
        sendNotification(title, messageBody, data, NotificationCompat.PRIORITY_DEFAULT);
    }
    
    private void sendNotification(String title, String messageBody, Map<String, String> data, int priority) {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        
        // Add data to intent
        if (data != null) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue());
            }
        }
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            intent, 
            PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE
        );
        
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        
        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title != null ? title : getString(R.string.app_name))
            .setContentText(messageBody)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            .setPriority(priority)
            .setColor(getResources().getColor(R.color.notification_color));
        
        // Add action buttons for certain notification types
        if (data != null) {
            String type = data.get("type");
            if ("project_update".equals(type)) {
                // Add "View Project" action
                Intent viewIntent = new Intent(this, MainActivity.class);
                viewIntent.putExtra("action", "view_project");
                viewIntent.putExtra("project_id", data.get("project_id"));
                
                PendingIntent viewPendingIntent = PendingIntent.getActivity(
                    this, 
                    1, 
                    viewIntent, 
                    PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE
                );
                
                notificationBuilder.addAction(
                    R.drawable.ic_view, 
                    "View Project", 
                    viewPendingIntent
                );
            }
        }
        
        NotificationManager notificationManager = 
            (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        
        // Generate unique notification ID
        int notificationId = (int) System.currentTimeMillis();
        
        notificationManager.notify(notificationId, notificationBuilder.build());
    }
    
    /**
     * Create notification channel for Android O and above
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.setLightColor(getResources().getColor(R.color.notification_color));
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
            
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    /**
     * Send registration token to server
     */
    private void sendRegistrationToServer(String token) {
        // Store token locally
        getSharedPreferences("fcm_prefs", MODE_PRIVATE)
            .edit()
            .putString("fcm_token", token)
            .apply();
        
        // TODO: Send token to your server
        Log.d(TAG, "FCM Token stored: " + token);
    }
    
    /**
     * Get stored FCM token
     */
    public static String getStoredToken(Context context) {
        return context.getSharedPreferences("fcm_prefs", Context.MODE_PRIVATE)
            .getString("fcm_token", null);
    }
}
